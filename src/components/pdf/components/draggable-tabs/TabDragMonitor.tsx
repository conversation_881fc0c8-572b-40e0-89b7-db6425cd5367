import useDragTab from "@/components/pdf/components/draggable-tabs/hooks/drag-tab.ts";
import { usePdfStore } from '@/store/pdf-store';
import { useEffect, useRef } from 'react';
import { useDragLayer } from 'react-dnd';
import { createPortal } from 'react-dom';
import { DragItem, DragTypes } from './types';

export const TabDragMonitor = ({}) => {
    const createdWindowIdRef = useRef<string | null>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const {
        createWindow,
        updateWindow,
        windows,
        setDragTabWindowId,
        setDragTabSourceWindowId
    } = usePdfStore((state) => ({
        createWindow: state.createWindow,
        updateWindow: state.updateWindow,
        windows: state.windows,
        setDragTabWindowId: state.setDragTabWindowId,
        setDragTabSourceWindowId: state.setDragTabSourceWindowId,
    }));

    const {
        isDragging,
        item,
        currentOffset,
    } = useDragLayer((monitor) => ({
        isDragging: monitor.isDragging(),
        item: monitor.getItem() as DragItem,
        currentOffset: monitor.getSourceClientOffset(),
    }));
    const {checkIfOutsideContainer, getTabWindowId} = useDragTab()

    useEffect(() => {
        // if (!isDragging || !item || item.type !== DragTypes.TAB || !currentOffset) {
        //     console.log("TabDragMonitor reset")
        //     // 重置状态
        //     createdWindowIdRef.current = null;
        //     setDragTabWindowId("")
        //     setDragTabSourceWindowId("")
        //     return;
        // }
        //
        // const sourceWindowId = getTabWindowId(item)
        // const sourceWindow = windows.get(sourceWindowId)
        // const isFromMainTabBar = sourceWindowId === 'main';
        //
        // // 检查是否脱离了标签栏区域
        // const isOutsideTabBar = checkIfOutsideContainer(item, currentOffset)
        // console.log("TabDragMonitor isOutsideTabBar", isOutsideTabBar)
        //
        // // 对于非主窗口，检查是否只有一个标签
        // const isSingleTabWindow = sourceWindow && sourceWindow.tabs.length === 1;
        //
        // // 决定拖拽行为
        // if (isOutsideTabBar && !createdWindowIdRef.current) {
        //     if (!isFromMainTabBar && sourceWindowId && isSingleTabWindow) {
        //         updateWindow(sourceWindowId, {position: {x: 99999, y: 0}})
        //     }
        //     // 计算窗口位置
        //     const windowPosition = {
        //         x: currentOffset.x,
        //         y: currentOffset.y + 49
        //     };
        //
        //     // 创建新窗口
        //     const windowId = createWindow([item.tabItem], windowPosition);
        //     createdWindowIdRef.current = windowId;
        //     setDragTabWindowId(windowId)
        //     setDragTabSourceWindowId(sourceWindowId)
        //
        //     console.log('Created window for dragged tab:', windowId);
        // } else if (createdWindowIdRef.current && currentOffset) {
        //     // 如果已经创建了窗口，让窗口跟随鼠标移动
        //     const newPosition = {
        //         x: currentOffset.x,
        //         y: currentOffset.y + 49
        //     }
        //     updateWindow(createdWindowIdRef.current!, {position: newPosition});
        // }
    }, [isDragging, item?.id, currentOffset?.x, currentOffset?.y]);

    useEffect(() => {
        const handleDragEnd = () => {
            console.log("TabDragMonitor handleDragEnd", containerRef.current, currentOffset, item, isDragging)
            if (!containerRef.current || !currentOffset || !item) return
            console.log('TabDragMonitor handleDragEnd start', containerRef.current.style.left, containerRef.current.style.width)
            containerRef.current.style.left = `${currentOffset.x - (item.width ?? 0) / 2}`
            containerRef.current.style.width = "800px"
            console.log('TabDragMonitor handleDragEnd end', containerRef.current.style.left, containerRef.current.style.width, currentOffset.x - (item.width ?? 0) / 2)

        };

        window.addEventListener('tabDragEnd', handleDragEnd);
        return () => {
            window.removeEventListener('tabDragEnd', handleDragEnd);
        };
    }, [currentOffset, item]);

    if (!currentOffset || !item || item.type !== DragTypes.TAB || !checkIfOutsideContainer(item, currentOffset)) {
        console.log("TabDragMonitor render stop")
        return null;
    }
    console.log("TabDragMonitor render")
    
    const dragContent = (
        <div className="bg-gray-50 border-b border border-gray-300 rounded-tl-lg rounded-tr-lg" style={{
            position: 'fixed',
            pointerEvents: 'none',
            zIndex: Date.now(),
            left: currentOffset.x + (item.width ?? 0) / 2,
            top: currentOffset.y,
            width: 800 - (item.width ?? 0) / 2,
            height: '50px',
        }}
             ref={containerRef}
        >

        </div>
    );

    return createPortal(dragContent, document.body);
};
