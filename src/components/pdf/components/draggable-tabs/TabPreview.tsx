import React from 'react';
import {DragItem} from './types';
import './styles.css'; // 确保引入样式文件

interface TabPreviewProps {
    item: DragItem;
}

/**
 * 这是一个自定义的标签拖拽预览组件。
 * 它的样式由 styles.css 中的 .drag-preview 类定义。
 */
export const TabPreview: React.FC<TabPreviewProps> = ({item}) => {
    // 你可以在这里设计任何你想要的预览样式
    return (
        <div className="drag-preview">
            <span>hehe</span>
        </div>
    );
};