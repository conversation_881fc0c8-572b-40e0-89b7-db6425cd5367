import React, {useCallback, useRef} from 'react';
import {useDrag, useDrop} from 'react-dnd';
import {CloseOutlined} from '@ant-design/icons';
import {DragItem, DragTypes, DropResult, TabItem} from './types';

interface DraggableTabProps {
    tab: TabItem;
    index: number;
    isActive: boolean;
    onTabClick: (key: string) => void;
    onTabClose: (key: string) => void;
    onTabMove: (dragIndex: number, hoverIndex: number) => void;
    onTabDragEnd: (item: DragItem, result: DropResult | null) => void;
    containerRef?: React.RefObject<HTMLElement | null>; // 标签容器的引用
}

export const DraggableTab: React.FC<DraggableTabProps> = ({
                                                              tab,
                                                              index,
                                                              isActive,
                                                              onTabClick,
                                                              onTabClose,
                                                              onTabMove,
                                                              onTabDragEnd,
                                                              containerRef,
                                                          }) => {
    const ref = useRef<HTMLDivElement>(null);
    const [isOutsideContainer, setIsOutsideContainer] = React.useState(false);

    // 检查是否脱离了容器区域
    const checkIfOutsideContainer = useCallback((monitor: any) => {
        if (!containerRef?.current || !monitor.getClientOffset()) {
            return {isOutside: false, position: null};
        }

        const containerRect = containerRef.current.getBoundingClientRect();
        const clientOffset = monitor.getClientOffset();

        // 添加一些容差，避免在边缘误触发
        const tolerance = 20;
        const isOutside = (
            clientOffset.x < containerRect.left - tolerance ||
            clientOffset.x > containerRect.right + tolerance ||
            clientOffset.y < containerRect.top - tolerance ||
            clientOffset.y > containerRect.bottom + tolerance
        );

        return {
            isOutside,
            position: isOutside ? {x: clientOffset.x, y: clientOffset.y} : null
        };
    }, [containerRef]);

    const [{isDragging}, drag] = useDrag({
        type: DragTypes.TAB,
        item: (): DragItem => {
            const sourceRect = ref.current?.getBoundingClientRect();

            return {
                type: DragTypes.TAB,
                id: tab.key,
                index,
                tabItem: tab,
                sourceRect,
            };
        },
        collect: (monitor) => {
            const isDragging = monitor.isDragging();

            // 检查是否在容器外
            if (isDragging) {
                const {isOutside} = checkIfOutsideContainer(monitor);
                setIsOutsideContainer(isOutside);
            } else {
                setIsOutsideContainer(false);
            }

            return {isDragging};
        },
        end: (item, monitor) => {
            const result = monitor.getDropResult<DropResult>();
            console.log("draggableTab", result)

            // 通知TabDragMonitor拖拽结束
            const dragEndEvent = new CustomEvent('tabDragEnd', {
                detail: {item, result}
            });
            document.dispatchEvent(dragEndEvent);

            // 如果没有明确的放置结果，但拖拽结束时在容器外，则创建新窗口
            if (!result) {
                const {isOutside, position} = checkIfOutsideContainer(monitor);
                if (isOutside && position) {
                    onTabDragEnd(item, {
                        type: 'new-window',
                        mousePosition: position
                    });
                } else {
                    onTabDragEnd(item, null);
                }
            } else {
                console.log("DraggableTab calling onTabDragEnd with result:", result);
                onTabDragEnd(item, result);
            }
        },
    });

    const [, drop] = useDrop({
        accept: DragTypes.TAB,
        hover: (item: DragItem, monitor) => {
            if (!ref.current) {
                return;
            }

            const dragIndex = item.index;
            const hoverIndex = index;

            // 如果拖拽的是同一个元素，不做处理
            if (dragIndex === hoverIndex) {
                return;
            }

            // 获取边界矩形
            const hoverBoundingRect = ref.current.getBoundingClientRect();

            // 获取中点
            const hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;

            // 获取鼠标位置
            const clientOffset = monitor.getClientOffset();

            if (!clientOffset) {
                return;
            }

            // 获取相对于悬停元素的鼠标位置
            const hoverClientX = clientOffset.x - hoverBoundingRect.left;

            // 只有当鼠标越过一半时才执行移动
            if (dragIndex < hoverIndex && hoverClientX < hoverMiddleX) {
                return;
            }

            if (dragIndex > hoverIndex && hoverClientX > hoverMiddleX) {
                return;
            }

            console.log("DraggableTab hovering over index:", dragIndex, hoverIndex)
            // 执行移动
            onTabMove(dragIndex, hoverIndex);

            // 更新拖拽项的索引
            item.index = hoverIndex;
        },
    });

    // 连接拖拽和放置
    drag(drop(ref));

    const handleClick = (e: React.MouseEvent) => {
        e.preventDefault();
        onTabClick(tab.key);
    };

    const handleClose = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        onTabClose(tab.key);
    };

    return (
        <div
            ref={ref}
            className={`
        group relative flex items-center px-3 py-2 cursor-pointer select-none
        border-r border-gray-200 min-w-0 max-w-48
        transition-all duration-200 ease-in-out
        ${isActive
                ? 'bg-white border-b-2 border-b-blue-500 text-blue-600'
                : 'bg-gray-50 hover:bg-gray-100 text-gray-700'
            }
        ${isDragging && isOutsideContainer ? 'opacity-0' : isDragging ? 'opacity-50' : ''}
      `}
            onClick={handleClick}
        >
            {/* 标签内容 */}
            <div className="flex items-center min-w-0 flex-1">
        <span className="truncate text-sm font-medium">
          {tab.label}
        </span>
            </div>

            {/* 关闭按钮 */}
            {tab.closable !== false && (
                <button
                    className={`
            ml-2 p-1 rounded-full opacity-0 group-hover:opacity-100
            hover:bg-gray-200 transition-opacity duration-200
            ${isActive ? 'opacity-70 hover:opacity-100' : ''}
          `}
                    onClick={handleClose}
                >
                    <CloseOutlined className="text-xs"/>
                </button>
            )}


        </div>
    );
};
